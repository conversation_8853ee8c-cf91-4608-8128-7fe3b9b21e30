<template>
  <base-card
    id="relation-source"
    class="y-page">
    <div class="y-bar search-bar">
      <h2 class="y-title">关系数据源管理</h2>
      <span>数据库类型</span>
      <el-select
        v-model="formData.SRC_DS_TYPE"
        placeholder="请选择数据库类型"
        clearable
        size="small"
        style="width: 160px">
        <el-option
          label="全部"
          value=""></el-option>
        <el-option
          label="MySQL"
          value="1"></el-option>
        <el-option
          label="Oracle"
          value="2"></el-option>
        <el-option
          label="SQL Server"
          value="3"></el-option>
        <el-option
          label="PostgreSQL"
          value="4"></el-option>
        <el-option
          label="DB2"
          value="5"></el-option>
        <el-option
          label="Sybase"
          value="6"></el-option>
        <el-option
          label="SQLite"
          value="7"></el-option>
      </el-select>
      <span>关键字</span>
      <el-input
        v-model="formData.SRC_DS_NAME"
        v-trim
        size="small"
        style="width: 220px"
        clearable
        placeholder="请输入关键字进行搜索"></el-input>
      <el-button
        v-debounce="fetchData"
        class="mini"
        type="primary">
        <i class="el-icon-search"></i>
        搜索
      </el-button>
      <el-button
        class="mini"
        type="primary"
        @click.native="openEdit(null)">
        <svg-icon icon="add"></svg-icon>
        新建关系数据源
      </el-button>
    </div>
    <empty-wrapper
      class="y-container--tight"
      :toggle="!sourceList || sourceList?.length === 0"
      v-loading="loading">
      <div class="template-item-wrapper y-card-wrapper y-container--tight no-padding">
        <config-card
          v-for="item in sourceList"
          :key="item.SRC_DS_ID"
          :data="item">
          <template #left>
            <h5 class="title">
              {{ item.SRC_DS_NAME }}
            </h5>
            <p class="desc"><span class="prefix">数据库名称：</span>{{ item.SYS_DS_NAME }}</p>
            <p class="desc" v-if="item._originalData?.DB_IP">
              <span class="prefix">连接地址：</span>{{ item._originalData.DB_IP }}:{{ item._originalData.DB_PORT }}
            </p>
            <div class="tags">
              <div class="tag">创建时间：{{ item.CREATE_TIME }}</div>
              <div class="tag" v-if="item._originalData?.DB_TYPE">数据库类型：{{ dbTypeMap[item._originalData.DB_TYPE] || item._originalData.DB_TYPE }}</div>
            </div>
          </template>
          <template #right>
            <div class="switch">
              <span class="prefix">启用状态：</span>
              <el-switch
                v-model="item.SRC_DS_STATE"
                @change="handleSwitch(item)"
                active-color="#52C41A"
                active-value="1"
                inactive-value="0">
              </el-switch>
            </div>
            <div class="btn-set">
              <el-button
                class="mini"
                type="danger"
                plain
                size="small"
                @click.native="deleteSource(item)"
                >删除</el-button
              >
              <el-button
                class="mini"
                type="primary"
                plain
                size="small"
                @click.native="openEdit(item)"
                >编辑</el-button
              >
              <!-- <el-button
                v-if="item._originalData?.DB_TYPE !== 'excel'"
                class="mini"
                type="primary"
                size="small"
                v-debounce="{ evtHandler: () => testSource(item) }"
                >链接测试</el-button
              > -->
            </div>
          </template>
        </config-card>
      </div>
    </empty-wrapper>
    <div class="footer">
      <pagination
        :current-page.sync="formData.pageIndex"
        :page-size.sync="formData.pageSize"
        :total="formData.pageTotal"
        @page="fetchData"
      ></pagination>
    </div>
    <el-drawer
      :title="editDrawerTitle"
      :visible.sync="editDrawerShow"
      direction="rtl"
      :size="680">
      <edit-form
        ref="editForm"
        :data="editDrawerData"
        @close-edit="closeEdit"
        @update-data="fetchData"></edit-form>
    </el-drawer>
  </base-card>
</template>

<script>
import { getList, testSource, deleteSource, updateStatus } from '@/api/source'
import EditForm from './components/EditForm.vue'
import ConfigCard from '@/components/ConfigCard'

export default {
  name: 'RelationSource',
  components: {
    ConfigCard,
    EditForm,
  },
  props: {},
  data() {
    return {
      loading: false,
      formData: {
        pageIndex: 1,
        pageSize: 15,
        pageTotal: 0,
        SRC_DS_NAME: '',
        SRC_DS_TYPE: '',
        SYS_DS_NAME: '',
      },
      sourceList: [],
      editDrawerTitle: '',
      editDrawerShow: false,
      editDrawerData: null,
    }
  },
  computed: {
    // 数据库类型映射
    dbTypeMap() {
      return {
        '1': 'MySQL',
        '2': 'Oracle',
        '3': 'SQL Server',
        '4': 'PostgreSQL',
        '5': 'DB2',
        '6': 'Sybase',
        '7': 'SQLite'
      }
    }
  },
  watch: {},
  created() {
    this.fetchData()
  },
  mounted() {},
  methods: {
    async fetchData() {
      this.loading = true

      // 构建查询参数
      const queryParams = {
        pageIndex: this.formData.pageIndex,
        pageSize: this.formData.pageSize,
        pageType: 3
      }

      // 如果有数据源名称搜索条件
      if (this.formData.SRC_DS_NAME && this.formData.SRC_DS_NAME.trim()) {
        queryParams.DATA_SOURCE_NAME = this.formData.SRC_DS_NAME.trim()
      }

      // 如果有数据库类型筛选条件
      if (this.formData.SRC_DS_TYPE && this.formData.SRC_DS_TYPE !== '') {
        queryParams.DB_TYPE = this.formData.SRC_DS_TYPE
      }

      const [err, res] = await getList(queryParams)
      if (!err && res && Array.isArray(res?.data)) {
        // 数据字段映射：新接口字段 → 原组件字段
        this.sourceList = res.data.map(item => ({
          SRC_DS_ID: item.ID,
          SRC_DS_NAME: item.DATA_SOURCE_NAME,
          SYS_DS_NAME: item.DB_NAME, // 使用数据库名作为系统数据源名称
          SRC_DS_STATE: item.STATUS,
          CREATE_TIME: item.CREATE_TIME,
          // 保留原始数据以备后续使用
          _originalData: item
        }))
        this.formData.pageTotal = res?.totalRow || 0
      } else {
        this.sourceList = []
        if (err) {
          console.error('获取数据源列表失败:', err)
        }
      }

      this.loading = false
    },
    openEdit(data) {
      if (data) {
        this.editDrawerData = data
        this.editDrawerTitle = '编辑关系数据源'
      } else {
        this.editDrawerData = null
        this.editDrawerTitle = '新建关系数据源'
      }
      this.editDrawerShow = true
    },
    closeEdit() {
      this.editDrawerShow = false
    },
    async handleSwitch(data) {
      // 构建更新状态的数据格式
      const payload = {
        id: data._originalData.ID,
        status: data.SRC_DS_STATE // 使用切换后的状态
      }

      const [err, res] = await updateStatus(payload)
      if (!err) {
        this.$message({
          message: res?.msg || '操作成功！',
          type: 'success',
          duration: 800,
          onClose: () => {},
        })
      } else {
        this.fetchData()
      }
    },
    async testSource(data) {
      const [err, res] = await testSource(data.SYS_DS_NAME, 'src')

      if (!err) {
        this.$message({
          message: '操作成功！',
          type: 'success',
          duration: 800,
          onClose: () => {},
        })
      }
    },
    async deleteSource(data) {
      try {
        await this.$confirm('此操作将永久删除选中数据源, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
      } catch (e) {
        return
      }
      const [err, res] = await deleteSource(data._originalData.ID)
      if (!err) {
        this.$message({
          message: res?.msg || '删除成功！',
          type: 'success',
          duration: 800,
          onClose: () => {
            this.fetchData()
          },
        })
      }
    },
  },
}
</script>

<style lang="scss">
#relation-source {
  @import '@/assets/style/modules/config-card-page.scss';
  @import "@/assets/style/modules/config-card-page-with-footer.scss";
  background-color: transparent;
}
</style>
