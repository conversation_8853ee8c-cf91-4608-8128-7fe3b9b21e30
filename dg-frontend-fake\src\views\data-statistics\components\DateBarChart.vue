<template>
  <div
    v-if="!data || Object.keys(data).length === 0"
    :key="0"
    class="y-container--tight no-padding">
    <el-empty
      :image="require('@/assets/images/no-data.png')"
      description="暂无数据"></el-empty>
  </div>
  <div
    v-else
    :key="1"
    class="date-bar-chart y-container no-padding"
    ref="chart-container"></div>
</template>

<script>
import chartMixins from '@/mixins/chartMixins.js'

export default {
  name: 'DateBarChart',
  components: {},
  mixins: [chartMixins],
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {}
  },
  computed: {
    // 处理日期数据，按时间排序
    sortedDateData() {
      if (!this.data || Object.keys(this.data).length === 0) {
        return []
      }
      
      return Object.entries(this.data)
        .map(([dateStr, value]) => ({
          dateStr,
          formattedDate: this.formatDate(dateStr),
          value: value || 0
        }))
        .sort((a, b) => a.dateStr.localeCompare(b.dateStr))
    },
    
    // X轴标签（格式化的日期）
    labelList() {
      return this.sortedDateData.map(item => item.formattedDate)
    },
    
    // Y轴数据（数值）
    dataList() {
      return this.sortedDateData.map(item => item.value)
    },
    
    // ECharts配置选项
    option() {
      return {
        grid: {
          top: '15%',
          bottom: '15%',
          left: '5%',
          right: '5%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          boundaryGap: true,
          axisLine: {
            show: true,
            lineStyle: {
              color: '#e8e8e8',
            },
          },
          axisLabel: {
            margin: this.nowSize(10),
            color: '#868686',
            fontSize: this.nowSize(14),
            rotate: 45, // 旋转标签避免重叠
          },
          axisTick: {
            show: false,
          },
          data: this.labelList,
        },
        yAxis: {
          type: 'value',
          axisLine: {
            show: false,
          },
          axisLabel: {
            margin: this.nowSize(10),
            color: '#868686',
            fontSize: this.nowSize(14),
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              color: '#f0f0f0',
            },
          },
        },
        series: [
          {
            name: '通话数量',
            type: 'bar',
            barWidth: '60%',
            itemStyle: {
              normal: {
                color: '#0555CE',
                borderRadius: [4, 4, 0, 0],
              },
              emphasis: {
                color: '#0444B8',
              },
            },
            label: {
              show: true,
              position: 'top',
              textStyle: {
                fontSize: this.nowSize(12),
                fontWeight: 'normal',
                color: '#666',
              },
            },
            data: this.dataList,
          },
        ],
        tooltip: {
          confine: true,
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: 'rgba(5, 85, 206, 0.1)',
            },
          },
          formatter: (params) => {
            if (params && params.length > 0) {
              const param = params[0]
              return `${param.name}<br/>通话数量: ${param.value}`
            }
            return ''
          },
          textStyle: {
            fontSize: this.nowSize(14),
          },
        },
        dataZoom: this.labelList.length > 10 ? [
          {
            type: 'slider',
            show: true,
            xAxisIndex: [0],
            height: this.nowSize(14),
            bottom: '5%',
            borderRadius: this.nowSize(7),
            borderColor: 'none',
            fillerColor: '#E8E8E8',
            backgroundColor: '#F2F4F7',
            moveHandleSize: 0,
          },
        ] : [],
      }
    },
  },
  beforeMount() {
    this.$nextTick(() => {
      if (this.chart) {
        this.chart.on('click', this.handleBarClick)
      }
    })
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.off('click', this.handleBarClick)
    }
  },
  methods: {
    // 格式化日期显示
    formatDate(dateStr) {
      if (dateStr && dateStr.length === 8) {
        const year = dateStr.substring(0, 4)
        const month = dateStr.substring(4, 6)
        const day = dateStr.substring(6, 8)
        return `${year}-${month}-${day}`
      }
      return dateStr
    },
    
    // 处理柱状图点击事件
    handleBarClick(param) {
      if (param && param.dataIndex !== undefined) {
        const clickedData = this.sortedDateData[param.dataIndex]
        if (clickedData) {
          // 发出事件，传递原始日期字符串
          this.$emit('date-change', clickedData.dateStr)
        }
      }
    },
  },
}
</script>

<style lang="scss">
.date-bar-chart {
  height: 100%;
}
</style>
